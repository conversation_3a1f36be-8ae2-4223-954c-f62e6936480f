import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Music, Users, LogIn, ArrowLeft, UserPlus } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { GuestService } from '@/services/guest.service';
import { sessionService } from '@/services/api.service';

export default function UnifiedJoinPage() {
  const [sessionCode, setSessionCode] = useState('');
  const [guestName, setGuestName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'code' | 'name' | 'account'>('code');
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login, isAuthenticated, user } = useAuth();

  const handleCodeSubmit = async () => {
    if (!sessionCode.trim()) {
      toast({
        title: "Code requis",
        description: "Veuillez entrer un code de session",
        variant: "destructive",
      });
      return;
    }

    if (isAuthenticated) {
      // Utilisateur connecté, rejoindre directement
      setIsLoading(true);
      try {
        await sessionService.join(sessionCode.toUpperCase());
        navigate(`/session/${sessionCode.toUpperCase()}`);
      } catch (error: any) {
        toast({
          title: "Erreur",
          description: error.response?.data?.error || "Impossible de rejoindre la session",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    } else {
      // Pas connecté, demander le nom ou proposer la connexion
      setStep('name');
    }
  };

  const handleGuestJoin = async () => {
    if (!guestName.trim()) {
      toast({
        title: "Nom requis",
        description: "Veuillez entrer votre nom",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Rejoindre en tant qu'invité
      const result = await GuestService.joinSession(sessionCode.trim(), guestName.trim());
      
      // Stocker les informations d'invité
      GuestService.storeGuestInfo(result.guestId, sessionCode.toUpperCase(), guestName.trim());
      
      toast({
        title: "Connexion réussie",
        description: `Bienvenue dans la session ${sessionCode.toUpperCase()}`,
      });
      
      navigate(`/session/${sessionCode.toUpperCase()}?guest=true`);
    } catch (error: any) {
      toast({
        title: "Erreur",
        description: error.response?.data?.error || "Impossible de rejoindre la session",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAccountLogin = async () => {
    setIsLoading(true);
    try {
      await login();
      // Après connexion, rediriger vers la session
      navigate(`/session/${sessionCode.toUpperCase()}`);
    } catch (error) {
      toast({
        title: "Erreur de connexion",
        description: "Impossible de se connecter avec Spotify",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="absolute top-4 left-4 text-white hover:bg-white/10"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          
          <div className="flex items-center justify-center mb-4">
            <Music className="h-12 w-12 text-purple-400 mr-4" />
            <h1 className="text-4xl font-bold text-white">Rejoindre une Session</h1>
          </div>
          <p className="text-xl text-purple-200">
            Entrez le code de session pour commencer
          </p>
        </div>

        <div className="max-w-md mx-auto">
          {step === 'code' && (
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader className="text-center">
                <CardTitle className="text-white flex items-center justify-center">
                  <Users className="h-6 w-6 mr-2 text-blue-400" />
                  Code de Session
                </CardTitle>
                <CardDescription className="text-gray-300">
                  Entrez le code partagé par l'hôte
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Input
                    type="text"
                    placeholder="Ex: ABCD12"
                    value={sessionCode}
                    onChange={(e) => setSessionCode(e.target.value.toUpperCase())}
                    className="text-center text-2xl font-mono tracking-widest bg-gray-700 border-gray-600 text-white"
                    maxLength={6}
                  />
                </div>
                
                <Button
                  onClick={handleCodeSubmit}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  disabled={!sessionCode.trim()}
                >
                  <Users className="mr-2 h-4 w-4" />
                  Continuer
                </Button>

                {isAuthenticated && user && (
                  <div className="text-center p-3 bg-green-900/20 rounded-lg border border-green-500/30">
                    <p className="text-green-400 text-sm">
                      ✅ Connecté en tant que <strong>{user.displayName}</strong>
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {step === 'name' && (
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader className="text-center">
                <CardTitle className="text-white flex items-center justify-center">
                  <UserPlus className="h-6 w-6 mr-2 text-green-400" />
                  Comment vous appeler ?
                </CardTitle>
                <CardDescription className="text-gray-300">
                  Session: <span className="font-mono text-blue-400">{sessionCode}</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Input
                    type="text"
                    placeholder="Votre nom ou pseudo"
                    value={guestName}
                    onChange={(e) => setGuestName(e.target.value)}
                    className="bg-gray-700 border-gray-600 text-white"
                    maxLength={30}
                  />
                </div>
                
                <Button
                  onClick={handleGuestJoin}
                  className="w-full bg-green-600 hover:bg-green-700"
                  disabled={!guestName.trim() || isLoading}
                >
                  {isLoading ? (
                    "Connexion..."
                  ) : (
                    <>
                      <Users className="mr-2 h-4 w-4" />
                      Rejoindre en tant qu'invité
                    </>
                  )}
                </Button>

                <div className="flex items-center space-x-2">
                  <div className="flex-1 h-px bg-gray-600"></div>
                  <span className="text-xs text-gray-400">ou</span>
                  <div className="flex-1 h-px bg-gray-600"></div>
                </div>

                <Button
                  onClick={handleAccountLogin}
                  variant="outline"
                  className="w-full border-green-600 text-green-400 hover:bg-green-600/10"
                  disabled={isLoading}
                >
                  <LogIn className="mr-2 h-4 w-4" />
                  Se connecter avec Spotify
                </Button>

                <div className="text-center p-3 bg-blue-900/20 rounded-lg border border-blue-500/30">
                  <p className="text-blue-200 text-xs">
                    💡 <strong>Astuce :</strong> Connectez-vous pour conserver vos points et statistiques !
                  </p>
                </div>

                <Button
                  onClick={() => setStep('code')}
                  variant="ghost"
                  className="w-full text-gray-400 hover:text-white"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Changer de code
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
