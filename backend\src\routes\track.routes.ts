import { Router } from 'express';
import { TrackController } from '../controllers/track.controller';
import { authMiddleware, guestAuthMiddleware } from '../middleware/auth.middleware';

const router = Router();

// Guest-friendly routes (allow both authenticated users and guests)
router.get('/search-guest', guestAuthMiddleware, TrackController.searchTracksForGuest);
router.post('/sessions/:sessionCode/tracks-guest', guestAuthMiddleware, TrackController.addTrackForGuest);
router.post('/sessions/:sessionCode/tracks/:trackId/vote-guest', guestAuthMiddleware, TrackController.voteTrackForGuest);

// All other track routes require authentication
router.use(authMiddleware);

// Track management
router.get('/search', TrackController.searchTracks);
router.post('/sessions/:sessionCode/tracks', TrackController.addTrack);
router.post('/sessions/:sessionCode/tracks/:trackId/vote', TrackController.voteTrack);
router.delete('/sessions/:sessionCode/tracks/:trackId', TrackController.removeTrack);
router.put('/sessions/:sessionCode/tracks/:trackId/playing', TrackController.updatePlayingTrack);

export default router;
