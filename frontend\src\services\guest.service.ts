import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface GuestJoinResponse {
  session: any;
  participant: any;
  guestId: string;
  isGuest: true;
}

export class GuestService {
  /**
   * Join a session as guest
   */
  static async joinSession(code: string, guestName: string): Promise<GuestJoinResponse> {
    const response = await axios.post(`${API_URL}/sessions/join-guest`, {
      code: code.toUpperCase(),
      guestName,
    });
    return response.data;
  }

  /**
   * Get session details as guest
   */
  static async getSession(code: string, guestId: string): Promise<any> {
    const response = await axios.get(`${API_URL}/sessions/guest/${code.toUpperCase()}`, {
      params: { guestId },
    });
    return response.data;
  }

  /**
   * Store guest info in localStorage
   */
  static storeGuestInfo(guestId: string, sessionCode: string, guestName: string): void {
    localStorage.setItem('guestInfo', JSON.stringify({
      guestId,
      sessionCode: sessionCode.toUpperCase(),
      guestName,
      isGuest: true,
      joinedAt: new Date().toISOString(),
    }));
  }

  /**
   * Get guest info from localStorage
   */
  static getGuestInfo(): {
    guestId: string;
    sessionCode: string;
    guestName: string;
    isGuest: boolean;
    joinedAt: string;
  } | null {
    const stored = localStorage.getItem('guestInfo');
    if (!stored) return null;
    
    try {
      return JSON.parse(stored);
    } catch {
      return null;
    }
  }

  /**
   * Clear guest info from localStorage
   */
  static clearGuestInfo(): void {
    localStorage.removeItem('guestInfo');
  }

  /**
   * Create axios instance with guest headers
   */
  private static createGuestAxios(guestId: string, sessionCode: string) {
    return axios.create({
      baseURL: API_URL,
      headers: {
        'Content-Type': 'application/json',
        'X-Guest-Id': guestId,
        'X-Session-Code': sessionCode.toUpperCase(),
      },
    });
  }

  /**
   * Search tracks as guest
   */
  static async searchTracks(
    guestId: string,
    sessionCode: string,
    query: string,
    limit = 20,
    offset = 0
  ): Promise<any> {
    const guestAxios = this.createGuestAxios(guestId, sessionCode);
    const response = await guestAxios.get('/tracks/search-guest', {
      params: { query, limit, offset },
    });
    return response.data;
  }

  /**
   * Add track as guest
   */
  static async addTrack(
    guestId: string,
    sessionCode: string,
    spotifyId: string
  ): Promise<any> {
    const guestAxios = this.createGuestAxios(guestId, sessionCode);
    const response = await guestAxios.post(`/tracks/sessions/${sessionCode.toUpperCase()}/tracks-guest`, {
      spotifyId,
    });
    return response.data;
  }

  /**
   * Vote on track as guest
   */
  static async voteTrack(
    guestId: string,
    sessionCode: string,
    trackId: string,
    emoji: string,
    value: number
  ): Promise<any> {
    const guestAxios = this.createGuestAxios(guestId, sessionCode);
    const response = await guestAxios.post(
      `/tracks/sessions/${sessionCode.toUpperCase()}/tracks/${trackId}/vote-guest`,
      { emoji, value }
    );
    return response.data;
  }

  /**
   * Check if user is currently a guest
   */
  static isGuest(): boolean {
    const guestInfo = this.getGuestInfo();
    return guestInfo?.isGuest === true;
  }
}
