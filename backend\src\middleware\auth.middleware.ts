import { Request, Response, NextFunction } from 'express';
import { verifyToken, JWTPayload } from '../utils/auth';
import { prisma } from '../config/database';

export interface AuthRequest extends Request {
  user?: JWTPayload;
  guest?: {
    guestId: string;
    sessionCode: string;
    sessionId: string;
    participantId: string;
  };
}

export function authMiddleware(req: AuthRequest, res: Response, next: NextFunction): void {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ error: 'No token provided' });
      return;
    }

    const token = authHeader.substring(7);
    const payload = verifyToken(token);

    req.user = payload;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
    return;
  }
}

/**
 * Middleware for guest authentication
 * Allows guests to access certain routes using guestId and sessionCode
 */
export async function guestAuthMiddleware(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
  try {
    // First try regular auth
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        const token = authHeader.substring(7);
        const payload = verifyToken(token);
        req.user = payload;
        next();
        return;
      } catch (error) {
        // Continue to guest auth if JWT fails
      }
    }

    // Try guest authentication
    const guestId = req.headers['x-guest-id'] as string;
    const sessionCode = req.headers['x-session-code'] as string;

    if (!guestId || !sessionCode) {
      res.status(401).json({ error: 'Authentication required - provide JWT token or guest credentials' });
      return;
    }

    // Verify guest exists and session is valid
    const session = await prisma.session.findUnique({
      where: { code: sessionCode.toUpperCase() },
      include: {
        participants: {
          where: { guestId: guestId }
        }
      }
    });

    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    if (!session.isActive) {
      res.status(400).json({ error: 'Session is no longer active' });
      return;
    }

    if (new Date() > session.expiresAt) {
      res.status(400).json({ error: 'Session has expired' });
      return;
    }

    const participant = session.participants.find(p => p.guestId === guestId);
    if (!participant) {
      res.status(403).json({ error: 'Guest not found in session' });
      return;
    }

    // Set guest info in request
    req.guest = {
      guestId,
      sessionCode: sessionCode.toUpperCase(),
      sessionId: session.id,
      participantId: participant.id
    };

    next();
  } catch (error) {
    console.error('Error in guest auth middleware:', error);
    res.status(500).json({ error: 'Authentication failed' });
    return;
  }
}

export function requireHost(req: AuthRequest, res: Response, next: NextFunction): void {
  if (!req.user || req.user.role !== 'HOST') {
    res.status(403).json({ error: 'Host access required' });
    return;
  }
  next();
}

export function requireParticipant(req: AuthRequest, res: Response, next: NextFunction): void {
  if (!req.user || (req.user.role !== 'PARTICIPANT' && req.user.role !== 'HOST')) {
    res.status(403).json({ error: 'Participant access required' });
    return;
  }
  next();
}
