import { Routes, Route } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/AuthContext';
import { ProtectedRoute } from '@/components/ProtectedRoute';
import HomePage from '@/pages/HomePage';
import CreateSessionPage from '@/pages/CreateSessionPage';
import JoinSessionPage from '@/pages/JoinSessionPage';
import GuestJoinPage from '@/pages/GuestJoinPage';
import UnifiedJoinPage from './pages/UnifiedJoinPage';
import SessionPage from '@/pages/SessionPage';
import AuthCallbackPage from '@/pages/AuthCallbackPage';

function App() {
  return (
    <AuthProvider>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/auth/callback" element={<AuthCallbackPage />} />
        <Route
          path="/create"
          element={
            <ProtectedRoute>
              <CreateSessionPage />
            </ProtectedRoute>
          }
        />
        <Route path="/join" element={<UnifiedJoinPage />} />
        <Route path="/guest" element={<GuestJoinPage />} />
        <Route
          path="/session/:sessionId"
          element={<SessionPage />}
        />
      </Routes>
      <Toaster />
    </AuthProvider>
  );
}

export default App;
